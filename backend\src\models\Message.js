const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  // Chat participants
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Sender is required']
  },
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Recipient is required']
  },
  
  // Message content
  content: {
    type: String,
    required: [true, 'Message content is required'],
    maxlength: [2000, 'Message cannot exceed 2000 characters']
  },
  messageType: {
    type: String,
    enum: ['text', 'image', 'file', 'location', 'booking-update', 'system'],
    default: 'text'
  },
  
  // Media attachments
  attachments: [{
    type: {
      type: String,
      enum: ['image', 'file', 'location'],
      required: true
    },
    url: String,
    filename: String,
    size: Number, // in bytes
    mimeType: String,
    // For location type
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    address: String
  }],
  
  // Message status
  status: {
    type: String,
    enum: ['sent', 'delivered', 'read'],
    default: 'sent'
  },
  
  // Read receipts
  readAt: Date,
  deliveredAt: Date,
  
  // Related booking (if applicable)
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking'
  },
  
  // Chat thread identification
  chatId: {
    type: String,
    required: true,
    index: true
  },
  
  // Message metadata
  isEdited: {
    type: Boolean,
    default: false
  },
  editedAt: Date,
  originalContent: String,
  
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedAt: Date,
  deletedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // System message data (for booking updates, etc.)
  systemData: {
    type: mongoose.Schema.Types.Mixed
  },
  
  // Reply to another message
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
messageSchema.index({ chatId: 1, createdAt: -1 });
messageSchema.index({ sender: 1, createdAt: -1 });
messageSchema.index({ recipient: 1, status: 1 });
messageSchema.index({ booking: 1, createdAt: -1 });

// Virtual for message age
messageSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  
  return this.createdAt.toLocaleDateString();
});

// Static method to generate chat ID between two users
messageSchema.statics.generateChatId = function(userId1, userId2) {
  const sortedIds = [userId1.toString(), userId2.toString()].sort();
  return `chat_${sortedIds[0]}_${sortedIds[1]}`;
};

// Static method to get chat history
messageSchema.statics.getChatHistory = function(userId1, userId2, limit = 50, skip = 0) {
  const chatId = this.generateChatId(userId1, userId2);
  
  return this.find({
    chatId,
    isDeleted: false
  })
  .populate('sender', 'firstName lastName profileImage')
  .populate('recipient', 'firstName lastName profileImage')
  .populate('replyTo', 'content sender createdAt')
  .sort({ createdAt: -1 })
  .limit(limit)
  .skip(skip);
};

// Static method to mark messages as read
messageSchema.statics.markAsRead = function(chatId, userId) {
  return this.updateMany({
    chatId,
    recipient: userId,
    status: { $ne: 'read' }
  }, {
    status: 'read',
    readAt: new Date()
  });
};

// Static method to get unread message count
messageSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({
    recipient: userId,
    status: { $ne: 'read' },
    isDeleted: false
  });
};

// Static method to get chat list for a user
messageSchema.statics.getChatList = async function(userId, limit = 20) {
  const pipeline = [
    // Match messages involving the user
    {
      $match: {
        $or: [
          { sender: mongoose.Types.ObjectId(userId) },
          { recipient: mongoose.Types.ObjectId(userId) }
        ],
        isDeleted: false
      }
    },
    // Sort by creation date (newest first)
    { $sort: { createdAt: -1 } },
    // Group by chatId to get the latest message for each chat
    {
      $group: {
        _id: '$chatId',
        lastMessage: { $first: '$$ROOT' },
        unreadCount: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$recipient', mongoose.Types.ObjectId(userId)] },
                  { $ne: ['$status', 'read'] }
                ]
              },
              1,
              0
            ]
          }
        }
      }
    },
    // Sort by last message date
    { $sort: { 'lastMessage.createdAt': -1 } },
    // Limit results
    { $limit: limit },
    // Populate user details
    {
      $lookup: {
        from: 'users',
        localField: 'lastMessage.sender',
        foreignField: '_id',
        as: 'lastMessage.sender'
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'lastMessage.recipient',
        foreignField: '_id',
        as: 'lastMessage.recipient'
      }
    },
    // Unwind populated arrays
    { $unwind: '$lastMessage.sender' },
    { $unwind: '$lastMessage.recipient' },
    // Project only needed fields
    {
      $project: {
        chatId: '$_id',
        lastMessage: {
          _id: '$lastMessage._id',
          content: '$lastMessage.content',
          messageType: '$lastMessage.messageType',
          createdAt: '$lastMessage.createdAt',
          status: '$lastMessage.status',
          sender: {
            _id: '$lastMessage.sender._id',
            firstName: '$lastMessage.sender.firstName',
            lastName: '$lastMessage.sender.lastName',
            profileImage: '$lastMessage.sender.profileImage'
          },
          recipient: {
            _id: '$lastMessage.recipient._id',
            firstName: '$lastMessage.recipient.firstName',
            lastName: '$lastMessage.recipient.lastName',
            profileImage: '$lastMessage.recipient.profileImage'
          }
        },
        unreadCount: 1,
        otherUser: {
          $cond: [
            { $eq: ['$lastMessage.sender._id', mongoose.Types.ObjectId(userId)] },
            '$lastMessage.recipient',
            '$lastMessage.sender'
          ]
        }
      }
    }
  ];
  
  return this.aggregate(pipeline);
};

// Pre-save middleware to set chatId and deliveredAt
messageSchema.pre('save', function(next) {
  if (this.isNew) {
    // Generate chatId if not provided
    if (!this.chatId) {
      this.chatId = this.constructor.generateChatId(this.sender, this.recipient);
    }
    
    // Set deliveredAt for new messages
    if (this.status === 'delivered' && !this.deliveredAt) {
      this.deliveredAt = new Date();
    }
  }
  
  // Set readAt when status changes to read
  if (this.isModified('status') && this.status === 'read' && !this.readAt) {
    this.readAt = new Date();
  }
  
  next();
});

// Method to soft delete message
messageSchema.methods.softDelete = function(deletedBy) {
  this.isDeleted = true;
  this.deletedAt = new Date();
  this.deletedBy = deletedBy;
  return this.save();
};

// Method to edit message
messageSchema.methods.editContent = function(newContent) {
  if (!this.isEdited) {
    this.originalContent = this.content;
  }
  this.content = newContent;
  this.isEdited = true;
  this.editedAt = new Date();
  return this.save();
};

module.exports = mongoose.model('Message', messageSchema);
