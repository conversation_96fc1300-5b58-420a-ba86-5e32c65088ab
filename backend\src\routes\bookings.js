const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Booking = require('../models/Booking');
const Service = require('../models/Service');
const Pet = require('../models/Pet');

const router = express.Router();

/**
 * @swagger
 * /api/bookings:
 *   get:
 *     summary: Get user's bookings
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *     responses:
 *       200:
 *         description: Bookings retrieved successfully
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  query('status').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build query - user can see bookings as customer or provider
    const query = {
      $or: [
        { customer: req.user.id },
        { provider: req.user.id }
      ]
    };

    if (req.query.status) {
      query.status = req.query.status;
    }

    const bookings = await Booking.find(query)
      .populate('customer', 'firstName lastName profileImage')
      .populate('provider', 'firstName lastName profileImage')
      .populate('service', 'title category')
      .populate('pets', 'name type profileImage')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Booking.countDocuments(query);

    res.json({
      success: true,
      data: {
        bookings,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get bookings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve bookings'
    });
  }
});

/**
 * @swagger
 * /api/bookings:
 *   post:
 *     summary: Create a new booking
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - serviceId
 *               - petIds
 *               - startDate
 *               - endDate
 *             properties:
 *               serviceId:
 *                 type: string
 *               petIds:
 *                 type: array
 *                 items:
 *                   type: string
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               startTime:
 *                 type: string
 *               endTime:
 *                 type: string
 *     responses:
 *       201:
 *         description: Booking created successfully
 *       400:
 *         description: Validation error
 */
router.post('/', [
  body('serviceId').isMongoId().withMessage('Valid service ID is required'),
  body('petIds').isArray({ min: 1 }).withMessage('At least one pet is required'),
  body('petIds.*').isMongoId().withMessage('Valid pet IDs are required'),
  body('startDate').isISO8601().withMessage('Valid start date is required'),
  body('endDate').isISO8601().withMessage('Valid end date is required'),
  body('startTime').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('endTime').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { serviceId, petIds, startDate, endDate, startTime, endTime, specialInstructions } = req.body;

    // Verify service exists and is active
    const service = await Service.findOne({ _id: serviceId, isActive: true })
      .populate('provider', 'firstName lastName');

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found or not available'
      });
    }

    // Verify pets belong to the user
    const pets = await Pet.find({
      _id: { $in: petIds },
      owner: req.user.id,
      isActive: true
    });

    if (pets.length !== petIds.length) {
      return res.status(400).json({
        success: false,
        message: 'One or more pets not found or not owned by you'
      });
    }

    // Calculate pricing
    const basePrice = service.pricing.basePrice;
    const additionalPetPrice = service.pricing.additionalPetPrice * Math.max(0, pets.length - 1);
    const subtotal = basePrice + additionalPetPrice;
    const tax = subtotal * 0.08; // 8% tax
    const serviceFee = subtotal * 0.05; // 5% service fee
    const total = subtotal + tax + serviceFee;

    // Create booking
    const booking = new Booking({
      customer: req.user.id,
      provider: service.provider._id,
      service: serviceId,
      pets: petIds,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      startTime,
      endTime,
      specialInstructions,
      pricing: {
        basePrice,
        additionalPetPrice,
        subtotal,
        tax,
        serviceFee,
        total,
        currency: service.pricing.currency
      },
      location: service.location
    });

    await booking.save();

    // Populate the booking for response
    await booking.populate([
      { path: 'customer', select: 'firstName lastName profileImage' },
      { path: 'provider', select: 'firstName lastName profileImage' },
      { path: 'service', select: 'title category' },
      { path: 'pets', select: 'name type profileImage' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      data: { booking }
    });

  } catch (error) {
    console.error('Create booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create booking'
    });
  }
});

/**
 * @swagger
 * /api/bookings/{id}:
 *   get:
 *     summary: Get booking by ID
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Booking retrieved successfully
 *       404:
 *         description: Booking not found
 */
router.get('/:id', async (req, res) => {
  try {
    const booking = await Booking.findOne({
      _id: req.params.id,
      $or: [
        { customer: req.user.id },
        { provider: req.user.id }
      ]
    })
    .populate('customer', 'firstName lastName profileImage phone')
    .populate('provider', 'firstName lastName profileImage phone')
    .populate('service', 'title category description')
    .populate('pets', 'name type breed profileImage');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    res.json({
      success: true,
      data: { booking }
    });

  } catch (error) {
    console.error('Get booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve booking'
    });
  }
});

module.exports = router;
