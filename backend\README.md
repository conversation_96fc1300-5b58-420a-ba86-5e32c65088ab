# Petric Backend API

Backend API for the Petric Pet Care Mobile Application built with Node.js, Express, and MongoDB.

## Features

- **Authentication & Authorization**: JWT-based auth with role-based access control
- **User Management**: User profiles, pet profiles, provider verification
- **Service Management**: Pet care service listings with location-based search
- **Booking System**: Complete booking workflow with calendar integration
- **Real-time Messaging**: Socket.IO powered chat system
- **Payment Processing**: Stripe integration for secure payments
- **File Uploads**: Image upload for profiles and pets
- **Admin Dashboard**: Administrative controls and analytics
- **API Documentation**: Swagger/OpenAPI documentation

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Payment**: Stripe API
- **Real-time**: Socket.IO
- **File Upload**: Multer
- **Validation**: Express Validator
- **Security**: Helmet, CORS, Rate Limiting
- **Documentation**: Swagger JSDoc

## Project Structure

```
backend/
├── src/
│   ├── controllers/     # Route controllers
│   ├── models/         # MongoDB models
│   ├── routes/         # API routes
│   ├── middleware/     # Custom middleware
│   ├── utils/          # Utility functions
│   └── config/         # Configuration files
├── uploads/            # File upload directory
├── tests/              # Test files
├── server.js           # Main server file
├── package.json        # Dependencies and scripts
└── README.md          # This file
```

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd petric/backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` file with your configuration values.

4. **Start MongoDB**
   Make sure MongoDB is running on your system.

5. **Run the application**
   ```bash
   # Development mode with auto-reload
   npm run dev
   
   # Production mode
   npm start
   ```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PORT` | Server port | No (default: 3000) |
| `NODE_ENV` | Environment mode | No (default: development) |
| `MONGODB_URI` | MongoDB connection string | Yes |
| `JWT_SECRET` | JWT signing secret | Yes |
| `JWT_EXPIRE` | JWT expiration time | No (default: 7d) |
| `EMAIL_HOST` | SMTP host for emails | Yes |
| `EMAIL_PORT` | SMTP port | Yes |
| `EMAIL_USER` | SMTP username | Yes |
| `EMAIL_PASS` | SMTP password | Yes |
| `STRIPE_SECRET_KEY` | Stripe secret key | Yes |
| `GOOGLE_MAPS_API_KEY` | Google Maps API key | Yes |

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/verify-email` - Verify email address
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/profile/image` - Upload profile image
- `PUT /api/users/notifications` - Update notification preferences

### Pets
- `GET /api/pets` - Get user's pets
- `POST /api/pets` - Add new pet
- `GET /api/pets/:id` - Get pet details
- `PUT /api/pets/:id` - Update pet
- `DELETE /api/pets/:id` - Delete pet

### Services
- `GET /api/services` - Get services with filtering
- `GET /api/services/:id` - Get service details
- `POST /api/services` - Create service (providers only)
- `PUT /api/services/:id` - Update service
- `DELETE /api/services/:id` - Delete service

### Bookings
- `GET /api/bookings` - Get user's bookings
- `POST /api/bookings` - Create new booking
- `GET /api/bookings/:id` - Get booking details
- `PUT /api/bookings/:id/status` - Update booking status

### Messages
- `GET /api/messages/chats` - Get chat list
- `GET /api/messages/chat/:userId` - Get chat history
- `POST /api/messages` - Send message
- `GET /api/messages/unread-count` - Get unread count

### Payments
- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/payments/confirm` - Confirm payment
- `GET /api/payments/history` - Get payment history
- `GET /api/payments/:id` - Get payment details

### Admin
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/users` - Manage users
- `GET /api/admin/bookings` - Manage bookings
- `GET /api/admin/services` - Manage services

## API Documentation

When running in development mode, API documentation is available at:
```
http://localhost:3000/api-docs
```

## Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## Default Test Users

For development and testing:

- **Customer**: <EMAIL> / password
- **Provider**: <EMAIL> / password  
- **Admin**: <EMAIL> / password

## Security Features

- JWT authentication with secure token handling
- Password hashing with bcrypt
- Rate limiting on authentication endpoints
- Input validation and sanitization
- CORS configuration
- Helmet for security headers
- File upload restrictions

## Real-time Features

- Socket.IO integration for real-time messaging
- Live booking status updates
- Push notification support via Firebase

## Deployment

1. Set `NODE_ENV=production`
2. Configure production database
3. Set up proper CORS origins
4. Configure email service
5. Set up Stripe webhook endpoints
6. Deploy to your preferred platform

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
