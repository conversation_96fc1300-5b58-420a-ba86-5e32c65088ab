const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
  provider: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Service provider is required']
  },
  title: {
    type: String,
    required: [true, 'Service title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Service description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  category: {
    type: String,
    required: [true, 'Service category is required'],
    enum: [
      'pet-boarding',
      'dog-walking',
      'pet-daycare',
      'drop-in-visits',
      'grooming',
      'vet-visits',
      'training',
      'pet-sitting',
      'other'
    ],
    lowercase: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  // Pricing
  pricing: {
    type: {
      type: String,
      enum: ['hourly', 'daily', 'per-visit', 'fixed'],
      required: true,
      default: 'hourly'
    },
    basePrice: {
      type: Number,
      required: [true, 'Base price is required'],
      min: [0, 'Price cannot be negative']
    },
    currency: {
      type: String,
      default: 'USD',
      uppercase: true
    },
    // Additional pricing options
    additionalPetPrice: {
      type: Number,
      default: 0,
      min: [0, 'Additional pet price cannot be negative']
    },
    holidayRate: {
      type: Number,
      default: 0,
      min: [0, 'Holiday rate cannot be negative']
    },
    overnightRate: {
      type: Number,
      default: 0,
      min: [0, 'Overnight rate cannot be negative']
    }
  },
  // Service details
  duration: {
    min: {
      type: Number,
      min: [0, 'Minimum duration cannot be negative']
    },
    max: {
      type: Number,
      min: [0, 'Maximum duration cannot be negative']
    },
    unit: {
      type: String,
      enum: ['minutes', 'hours', 'days'],
      default: 'hours'
    }
  },
  // Pet specifications
  petTypes: [{
    type: String,
    enum: ['dog', 'cat', 'bird', 'rabbit', 'hamster', 'fish', 'reptile', 'other'],
    lowercase: true
  }],
  petSizes: [{
    type: String,
    enum: ['small', 'medium', 'large', 'extra-large'],
    lowercase: true
  }],
  maxPets: {
    type: Number,
    default: 1,
    min: [1, 'Must accept at least 1 pet']
  },
  // Location and availability
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true
    },
    address: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String
    }
  },
  serviceArea: {
    radius: {
      type: Number,
      default: 10, // km
      min: [0, 'Service radius cannot be negative']
    },
    unit: {
      type: String,
      enum: ['km', 'miles'],
      default: 'km'
    }
  },
  // Availability
  availability: {
    schedule: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
        required: true
      },
      isAvailable: {
        type: Boolean,
        default: true
      },
      timeSlots: [{
        startTime: {
          type: String,
          match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
        },
        endTime: {
          type: String,
          match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
        }
      }]
    }],
    blackoutDates: [{
      startDate: Date,
      endDate: Date,
      reason: String
    }],
    advanceBooking: {
      min: {
        type: Number,
        default: 24, // hours
        min: [0, 'Minimum advance booking cannot be negative']
      },
      max: {
        type: Number,
        default: 720, // hours (30 days)
        min: [0, 'Maximum advance booking cannot be negative']
      }
    }
  },
  // Service features and amenities
  features: [{
    type: String,
    trim: true
  }],
  amenities: [{
    type: String,
    trim: true
  }],
  // Requirements and policies
  requirements: {
    vaccination: {
      type: Boolean,
      default: true
    },
    spayedNeutered: {
      type: Boolean,
      default: false
    },
    meetAndGreet: {
      type: Boolean,
      default: false
    },
    customRequirements: [String]
  },
  policies: {
    cancellation: {
      type: String,
      maxlength: [500, 'Cancellation policy cannot exceed 500 characters']
    },
    refund: {
      type: String,
      maxlength: [500, 'Refund policy cannot exceed 500 characters']
    },
    additional: {
      type: String,
      maxlength: [1000, 'Additional policies cannot exceed 1000 characters']
    }
  },
  // Media
  images: [{
    url: String,
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  // Reviews and ratings
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    },
    breakdown: {
      5: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      1: { type: Number, default: 0 }
    }
  },
  // Service statistics
  stats: {
    totalBookings: {
      type: Number,
      default: 0
    },
    completedBookings: {
      type: Number,
      default: 0
    },
    cancelledBookings: {
      type: Number,
      default: 0
    },
    responseTime: {
      type: Number, // in minutes
      default: 0
    }
  },
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  // SEO and search
  tags: [{
    type: String,
    lowercase: true,
    trim: true
  }],
  searchKeywords: [{
    type: String,
    lowercase: true,
    trim: true
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Geospatial index for location-based searches
serviceSchema.index({ location: '2dsphere' });

// Compound indexes for efficient queries
serviceSchema.index({ category: 1, isActive: 1 });
serviceSchema.index({ provider: 1, isActive: 1 });
serviceSchema.index({ 'rating.average': -1, isActive: 1 });
serviceSchema.index({ 'pricing.basePrice': 1, isActive: 1 });
serviceSchema.index({ tags: 1, isActive: 1 });

// Text index for search functionality
serviceSchema.index({
  title: 'text',
  description: 'text',
  tags: 'text',
  searchKeywords: 'text'
});

// Virtual for primary image
serviceSchema.virtual('primaryImage').get(function() {
  const primaryImg = this.images.find(img => img.isPrimary);
  return primaryImg ? primaryImg.url : (this.images.length > 0 ? this.images[0].url : null);
});

// Virtual for display price
serviceSchema.virtual('displayPrice').get(function() {
  const price = this.pricing.basePrice;
  const currency = this.pricing.currency;
  const type = this.pricing.type;
  
  let suffix = '';
  switch (type) {
    case 'hourly':
      suffix = '/hour';
      break;
    case 'daily':
      suffix = '/day';
      break;
    case 'per-visit':
      suffix = '/visit';
      break;
    default:
      suffix = '';
  }
  
  return `${currency} ${price}${suffix}`;
});

// Method to check if service is available on a specific date/time
serviceSchema.methods.isAvailableAt = function(date, startTime, endTime) {
  const dayName = date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  
  // Check if it's a blackout date
  const isBlackedOut = this.availability.blackoutDates.some(blackout => {
    return date >= blackout.startDate && date <= blackout.endDate;
  });
  
  if (isBlackedOut) return false;
  
  // Check day availability
  const daySchedule = this.availability.schedule.find(s => s.day === dayName);
  if (!daySchedule || !daySchedule.isAvailable) return false;
  
  // Check time slot availability
  if (startTime && endTime) {
    return daySchedule.timeSlots.some(slot => {
      return startTime >= slot.startTime && endTime <= slot.endTime;
    });
  }
  
  return true;
};

// Pre-save middleware to ensure only one primary image
serviceSchema.pre('save', function(next) {
  if (this.isModified('images')) {
    const primaryImages = this.images.filter(img => img.isPrimary);
    if (primaryImages.length > 1) {
      // Keep only the first primary image
      this.images.forEach((img, index) => {
        if (img.isPrimary && index > 0) {
          img.isPrimary = false;
        }
      });
    }
  }
  next();
});

module.exports = mongoose.model('Service', serviceSchema);
