{"name": "jest-worker", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-worker"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@types/node": "*", "@ungap/structured-clone": "^1.3.0", "jest-util": "30.0.2", "merge-stream": "^2.0.0", "supports-color": "^8.1.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.3", "@types/ungap__structured-clone": "^1.2.0", "get-stream": "^6.0.0", "jest-leak-detector": "30.0.2", "worker-farm": "^1.7.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c"}