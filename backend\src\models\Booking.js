const mongoose = require('mongoose');

const bookingSchema = new mongoose.Schema({
  // Core booking information
  bookingId: {
    type: String,
    unique: true,
    required: true,
    index: false // Remove duplicate index
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  provider: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Provider is required']
  },
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
    required: [true, 'Service is required']
  },
  pets: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Pet',
    required: true
  }],
  
  // Booking dates and times
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required']
  },
  startTime: {
    type: String,
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid start time format (HH:MM)']
  },
  endTime: {
    type: String,
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid end time format (HH:MM)']
  },
  duration: {
    value: Number,
    unit: {
      type: String,
      enum: ['minutes', 'hours', 'days'],
      default: 'hours'
    }
  },
  
  // Booking status
  status: {
    type: String,
    enum: [
      'pending',      // Waiting for provider confirmation
      'confirmed',    // Provider confirmed
      'in-progress',  // Service is currently being provided
      'completed',    // Service completed successfully
      'cancelled',    // Cancelled by customer or provider
      'no-show',      // Customer didn't show up
      'refunded'      // Booking was refunded
    ],
    default: 'pending'
  },
  
  // Pricing and payment
  pricing: {
    basePrice: {
      type: Number,
      required: true,
      min: [0, 'Base price cannot be negative']
    },
    additionalPetPrice: {
      type: Number,
      default: 0,
      min: [0, 'Additional pet price cannot be negative']
    },
    holidayRate: {
      type: Number,
      default: 0,
      min: [0, 'Holiday rate cannot be negative']
    },
    overnightRate: {
      type: Number,
      default: 0,
      min: [0, 'Overnight rate cannot be negative']
    },
    subtotal: {
      type: Number,
      required: true,
      min: [0, 'Subtotal cannot be negative']
    },
    tax: {
      type: Number,
      default: 0,
      min: [0, 'Tax cannot be negative']
    },
    serviceFee: {
      type: Number,
      default: 0,
      min: [0, 'Service fee cannot be negative']
    },
    discount: {
      amount: {
        type: Number,
        default: 0,
        min: [0, 'Discount amount cannot be negative']
      },
      code: String,
      reason: String
    },
    total: {
      type: Number,
      required: true,
      min: [0, 'Total cannot be negative']
    },
    currency: {
      type: String,
      default: 'USD',
      uppercase: true
    }
  },
  
  // Payment information
  payment: {
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded', 'partially-refunded'],
      default: 'pending'
    },
    method: {
      type: String,
      enum: ['card', 'paypal', 'apple-pay', 'google-pay', 'bank-transfer'],
      default: 'card'
    },
    stripePaymentIntentId: String,
    stripeChargeId: String,
    paidAt: Date,
    refundedAt: Date,
    refundAmount: {
      type: Number,
      default: 0,
      min: [0, 'Refund amount cannot be negative']
    },
    refundReason: String
  },
  
  // Special instructions and notes
  specialInstructions: {
    type: String,
    maxlength: [2000, 'Special instructions cannot exceed 2000 characters']
  },
  customerNotes: {
    type: String,
    maxlength: [1000, 'Customer notes cannot exceed 1000 characters']
  },
  providerNotes: {
    type: String,
    maxlength: [1000, 'Provider notes cannot exceed 1000 characters']
  },
  
  // Location information
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: [Number], // [longitude, latitude]
    address: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String
    }
  },
  
  // Communication and updates
  messages: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  }],
  
  // Service completion
  completion: {
    completedAt: Date,
    completionNotes: String,
    photos: [String], // URLs to completion photos
    rating: {
      customerRating: {
        type: Number,
        min: 1,
        max: 5
      },
      providerRating: {
        type: Number,
        min: 1,
        max: 5
      }
    }
  },
  
  // Cancellation information
  cancellation: {
    cancelledAt: Date,
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: String,
    refundEligible: {
      type: Boolean,
      default: false
    }
  },
  
  // Recurring booking information
  recurring: {
    isRecurring: {
      type: Boolean,
      default: false
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'bi-weekly', 'monthly'],
      default: 'weekly'
    },
    endDate: Date,
    parentBooking: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Booking'
    },
    childBookings: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Booking'
    }]
  },
  
  // Emergency contact information
  emergencyContact: {
    name: String,
    phone: String,
    relationship: String
  },
  
  // Tracking and notifications
  notifications: {
    reminderSent: {
      type: Boolean,
      default: false
    },
    confirmationSent: {
      type: Boolean,
      default: false
    },
    completionSent: {
      type: Boolean,
      default: false
    }
  },
  
  // Metadata
  source: {
    type: String,
    enum: ['mobile-app', 'web', 'admin'],
    default: 'mobile-app'
  },
  
  // Timestamps for status changes
  statusHistory: [{
    status: String,
    changedAt: {
      type: Date,
      default: Date.now
    },
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: String
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
bookingSchema.index({ bookingId: 1 }, { unique: true });
bookingSchema.index({ customer: 1, status: 1 });
bookingSchema.index({ provider: 1, status: 1 });
bookingSchema.index({ service: 1, status: 1 });
bookingSchema.index({ startDate: 1, endDate: 1 });
bookingSchema.index({ status: 1, startDate: 1 });
bookingSchema.index({ 'payment.status': 1 });

// Virtual for booking duration in a readable format
bookingSchema.virtual('displayDuration').get(function() {
  if (!this.duration) return 'Duration not specified';
  
  const { value, unit } = this.duration;
  return `${value} ${unit}${value !== 1 ? 's' : ''}`;
});

// Virtual for total service time
bookingSchema.virtual('totalHours').get(function() {
  const start = new Date(`${this.startDate.toDateString()} ${this.startTime}`);
  const end = new Date(`${this.endDate.toDateString()} ${this.endTime}`);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 0;
  }
  
  return Math.abs(end - start) / (1000 * 60 * 60); // Convert to hours
});

// Virtual for booking status display
bookingSchema.virtual('statusDisplay').get(function() {
  return this.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
});

// Pre-save middleware to generate booking ID
bookingSchema.pre('save', async function(next) {
  if (this.isNew && !this.bookingId) {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // Find the last booking of the day to generate sequential number
    const lastBooking = await this.constructor
      .findOne({ bookingId: new RegExp(`^PET${year}${month}${day}`) })
      .sort({ bookingId: -1 });
    
    let sequence = 1;
    if (lastBooking) {
      const lastSequence = parseInt(lastBooking.bookingId.slice(-3));
      sequence = lastSequence + 1;
    }
    
    this.bookingId = `PET${year}${month}${day}${sequence.toString().padStart(3, '0')}`;
  }
  
  // Add status change to history
  if (this.isModified('status') && !this.isNew) {
    this.statusHistory.push({
      status: this.status,
      changedAt: new Date(),
      changedBy: this._statusChangedBy || null,
      reason: this._statusChangeReason || null
    });
  }
  
  next();
});

// Method to check if booking can be cancelled
bookingSchema.methods.canBeCancelled = function() {
  const now = new Date();
  const bookingStart = new Date(`${this.startDate.toDateString()} ${this.startTime}`);
  const hoursUntilBooking = (bookingStart - now) / (1000 * 60 * 60);
  
  return ['pending', 'confirmed'].includes(this.status) && hoursUntilBooking > 24;
};

// Method to calculate refund amount
bookingSchema.methods.calculateRefund = function() {
  if (!this.canBeCancelled()) return 0;
  
  const now = new Date();
  const bookingStart = new Date(`${this.startDate.toDateString()} ${this.startTime}`);
  const hoursUntilBooking = (bookingStart - now) / (1000 * 60 * 60);
  
  // Full refund if cancelled more than 48 hours in advance
  if (hoursUntilBooking > 48) {
    return this.pricing.total;
  }
  
  // 50% refund if cancelled 24-48 hours in advance
  if (hoursUntilBooking > 24) {
    return this.pricing.total * 0.5;
  }
  
  return 0;
};

module.exports = mongoose.model('Booking', bookingSchema);
