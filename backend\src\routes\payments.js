const express = require('express');
const { body, validationResult } = require('express-validator');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Payment = require('../models/Payment');
const Booking = require('../models/Booking');

const router = express.Router();

/**
 * @swagger
 * /api/payments/create-intent:
 *   post:
 *     summary: Create payment intent for booking
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bookingId
 *             properties:
 *               bookingId:
 *                 type: string
 *               paymentMethodId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Payment intent created successfully
 *       404:
 *         description: Booking not found
 */
router.post('/create-intent', [
  body('bookingId').isMongoId().withMessage('Valid booking ID is required'),
  body('paymentMethodId').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { bookingId, paymentMethodId } = req.body;

    // Find booking
    const booking = await Booking.findOne({
      _id: bookingId,
      customer: req.user.id
    }).populate('provider', 'firstName lastName');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if payment already exists
    let payment = await Payment.findOne({ booking: bookingId });
    
    if (!payment) {
      // Create new payment record
      payment = new Payment({
        booking: bookingId,
        customer: req.user.id,
        provider: booking.provider._id,
        amount: {
          subtotal: booking.pricing.subtotal,
          tax: booking.pricing.tax,
          serviceFee: booking.pricing.serviceFee,
          total: booking.pricing.total,
          currency: booking.pricing.currency
        },
        paymentMethod: {
          type: 'card'
        }
      });
      await payment.save();
    }

    // Create Stripe payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(booking.pricing.total * 100), // Convert to cents
      currency: booking.pricing.currency.toLowerCase(),
      payment_method: paymentMethodId,
      confirmation_method: 'manual',
      confirm: paymentMethodId ? true : false,
      metadata: {
        bookingId: bookingId,
        paymentId: payment.paymentId,
        customerId: req.user.id.toString()
      }
    });

    // Update payment with Stripe data
    payment.stripe.paymentIntentId = paymentIntent.id;
    payment.stripe.clientSecret = paymentIntent.client_secret;
    
    if (paymentIntent.status === 'succeeded') {
      payment.status = 'succeeded';
      payment.timeline.capturedAt = new Date();
      booking.payment.status = 'paid';
      booking.payment.paidAt = new Date();
      await booking.save();
    }
    
    await payment.save();

    res.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        status: paymentIntent.status,
        payment: {
          id: payment.paymentId,
          amount: payment.amount,
          status: payment.status
        }
      }
    });

  } catch (error) {
    console.error('Create payment intent error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment intent'
    });
  }
});

/**
 * @swagger
 * /api/payments/confirm:
 *   post:
 *     summary: Confirm payment
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - paymentIntentId
 *             properties:
 *               paymentIntentId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Payment confirmed successfully
 */
router.post('/confirm', [
  body('paymentIntentId').notEmpty().withMessage('Payment intent ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { paymentIntentId } = req.body;

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    // Find payment record
    const payment = await Payment.findOne({
      'stripe.paymentIntentId': paymentIntentId,
      customer: req.user.id
    });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Update payment status based on Stripe status
    if (paymentIntent.status === 'succeeded') {
      payment.status = 'succeeded';
      payment.timeline.capturedAt = new Date();
      
      // Update booking payment status
      await Booking.findByIdAndUpdate(payment.booking, {
        'payment.status': 'paid',
        'payment.paidAt': new Date(),
        'payment.stripePaymentIntentId': paymentIntentId
      });
    } else if (paymentIntent.status === 'requires_action') {
      payment.status = 'processing';
    } else if (paymentIntent.status === 'payment_failed') {
      payment.status = 'failed';
      payment.timeline.failedAt = new Date();
    }

    await payment.save();

    res.json({
      success: true,
      data: {
        status: paymentIntent.status,
        payment: {
          id: payment.paymentId,
          status: payment.status
        }
      }
    });

  } catch (error) {
    console.error('Confirm payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to confirm payment'
    });
  }
});

/**
 * @swagger
 * /api/payments/history:
 *   get:
 *     summary: Get payment history
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Payment history retrieved successfully
 */
router.get('/history', async (req, res) => {
  try {
    const payments = await Payment.find({
      customer: req.user.id
    })
    .populate('booking', 'bookingId startDate service')
    .populate('booking.service', 'title category')
    .sort({ createdAt: -1 })
    .limit(50);

    res.json({
      success: true,
      data: { payments }
    });

  } catch (error) {
    console.error('Get payment history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve payment history'
    });
  }
});

/**
 * @swagger
 * /api/payments/{id}:
 *   get:
 *     summary: Get payment details
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Payment details retrieved successfully
 *       404:
 *         description: Payment not found
 */
router.get('/:id', async (req, res) => {
  try {
    const payment = await Payment.findOne({
      _id: req.params.id,
      customer: req.user.id
    })
    .populate('booking', 'bookingId startDate endDate service pets')
    .populate('booking.service', 'title category')
    .populate('booking.pets', 'name type');

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    res.json({
      success: true,
      data: { payment }
    });

  } catch (error) {
    console.error('Get payment details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve payment details'
    });
  }
});

module.exports = router;
