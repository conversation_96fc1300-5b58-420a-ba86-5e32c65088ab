// SMS utility using a simple mock implementation
// In production, you would integrate with services like Twilio, AWS SNS, etc.

const smsTemplates = {
  'phone-verification': 'Your Petric verification code is: {{code}}. This code will expire in 10 minutes.',
  'booking-reminder': 'Reminder: Your pet care service with {{providerName}} is scheduled for {{time}} tomorrow. Booking ID: {{bookingId}}',
  'booking-confirmed': 'Your Petric booking ({{bookingId}}) has been confirmed! Service: {{serviceName}} on {{date}} at {{time}}.',
  'booking-cancelled': 'Your Petric booking ({{bookingId}}) has been cancelled. If you have questions, please contact support.',
  'service-started': 'Your pet care service has started! Your provider {{providerName}} is now caring for {{petNames}}.',
  'service-completed': 'Your pet care service is complete! Please rate your experience in the Petric app.'
};

// Replace template variables
const replaceTemplateVariables = (template, data) => {
  let result = template;
  for (const [key, value] of Object.entries(data)) {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, value || '');
  }
  return result;
};

// Generate verification code
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
};

// Mock SMS sending function
// In production, replace this with actual SMS service integration
const sendSMS = async ({ to, message, template, data = {} }) => {
  try {
    let smsMessage = message;
    
    // Use template if provided
    if (template && smsTemplates[template]) {
      smsMessage = replaceTemplateVariables(smsTemplates[template], data);
    }
    
    // Mock SMS sending - log to console
    console.log(`SMS Mock Send:
      To: ${to}
      Message: ${smsMessage}
      Timestamp: ${new Date().toISOString()}
    `);
    
    // Simulate API response
    const mockResponse = {
      success: true,
      messageId: `sms_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      to,
      message: smsMessage,
      status: 'sent',
      timestamp: new Date().toISOString()
    };
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return mockResponse;
    
  } catch (error) {
    console.error('SMS sending failed:', error);
    throw error;
  }
};

// Send verification code
const sendVerificationCode = async (phoneNumber) => {
  const code = generateVerificationCode();
  
  try {
    const result = await sendSMS({
      to: phoneNumber,
      template: 'phone-verification',
      data: { code }
    });
    
    return {
      success: true,
      code, // In production, don't return the code - store it securely
      messageId: result.messageId
    };
    
  } catch (error) {
    console.error('Failed to send verification code:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Send booking notification
const sendBookingNotification = async (phoneNumber, type, bookingData) => {
  try {
    let template;
    let data = bookingData;
    
    switch (type) {
      case 'confirmed':
        template = 'booking-confirmed';
        break;
      case 'reminder':
        template = 'booking-reminder';
        break;
      case 'cancelled':
        template = 'booking-cancelled';
        break;
      case 'started':
        template = 'service-started';
        break;
      case 'completed':
        template = 'service-completed';
        break;
      default:
        throw new Error('Invalid notification type');
    }
    
    const result = await sendSMS({
      to: phoneNumber,
      template,
      data
    });
    
    return result;
    
  } catch (error) {
    console.error('Failed to send booking notification:', error);
    throw error;
  }
};

// Send bulk SMS
const sendBulkSMS = async (messages) => {
  const results = [];
  
  for (const messageData of messages) {
    try {
      const result = await sendSMS(messageData);
      results.push({ success: true, messageId: result.messageId, to: messageData.to });
    } catch (error) {
      results.push({ success: false, error: error.message, to: messageData.to });
    }
  }
  
  return results;
};

// Validate phone number format
const validatePhoneNumber = (phoneNumber) => {
  // Basic phone number validation
  const phoneRegex = /^\+?[\d\s-()]+$/;
  return phoneRegex.test(phoneNumber) && phoneNumber.replace(/\D/g, '').length >= 10;
};

// Format phone number
const formatPhoneNumber = (phoneNumber) => {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Add country code if not present (assuming US +1)
  if (cleaned.length === 10) {
    return `+1${cleaned}`;
  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `+${cleaned}`;
  }
  
  return phoneNumber; // Return as-is if already formatted or international
};

// Check SMS delivery status (mock implementation)
const checkDeliveryStatus = async (messageId) => {
  // Mock delivery status check
  const statuses = ['sent', 'delivered', 'failed'];
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
  
  return {
    messageId,
    status: randomStatus,
    timestamp: new Date().toISOString()
  };
};

module.exports = {
  sendSMS,
  sendVerificationCode,
  sendBookingNotification,
  sendBulkSMS,
  validatePhoneNumber,
  formatPhoneNumber,
  generateVerificationCode,
  checkDeliveryStatus,
  smsTemplates
};
