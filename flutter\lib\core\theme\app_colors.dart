import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF4CAF50);
  static const Color secondary = Color(0xFF81C784);
  static const Color background = Color(0xFFF8F8F8);
  static const Color text = Color(0xFF212121);
  static const Color danger = Color(0xFFE57373);
  
  // Primary Color Variations
  static const Color primaryLight = Color(0xFF80E27E);
  static const Color primaryDark = Color(0xFF087F23);
  
  // Secondary Color Variations
  static const Color secondaryLight = Color(0xFFB2DFDB);
  static const Color secondaryDark = Color(0xFF4DB6AC);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color greyLight = Color(0xFFE0E0E0);
  static const Color greyDark = Color(0xFF616161);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFE57373);
  static const Color info = Color(0xFF2196F3);
  
  // Background Variations
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF3F3F3);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textDisabled = Color(0xFF9E9E9E);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFFFFFFFF);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderLight = Color(0xFFF0F0F0);
  static const Color borderDark = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x26000000);
  
  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  static const Color overlayDark = Color(0xB3000000);
  
  // Pet Category Colors
  static const Color dogColor = Color(0xFFFF7043);
  static const Color catColor = Color(0xFF9C27B0);
  static const Color birdColor = Color(0xFF03DAC6);
  static const Color rabbitColor = Color(0xFFFF5722);
  static const Color fishColor = Color(0xFF00BCD4);
  static const Color reptileColor = Color(0xFF8BC34A);
  static const Color otherColor = Color(0xFF607D8B);
  
  // Service Category Colors
  static const Color boardingColor = Color(0xFF3F51B5);
  static const Color walkingColor = Color(0xFF4CAF50);
  static const Color daycareColor = Color(0xFFFF9800);
  static const Color visitColor = Color(0xFF9C27B0);
  static const Color groomingColor = Color(0xFFE91E63);
  static const Color vetColor = Color(0xFFF44336);
  static const Color trainingColor = Color(0xFF2196F3);
  static const Color sittingColor = Color(0xFF795548);
  
  // Booking Status Colors
  static const Color pendingColor = Color(0xFFFF9800);
  static const Color confirmedColor = Color(0xFF4CAF50);
  static const Color inProgressColor = Color(0xFF2196F3);
  static const Color completedColor = Color(0xFF4CAF50);
  static const Color cancelledColor = Color(0xFFE57373);
  static const Color refundedColor = Color(0xFF9C27B0);
  
  // Rating Colors
  static const Color ratingFilled = Color(0xFFFFD700);
  static const Color ratingEmpty = Color(0xFFE0E0E0);
  
  // Social Media Colors
  static const Color google = Color(0xFFDB4437);
  static const Color apple = Color(0xFF000000);
  static const Color facebook = Color(0xFF4267B2);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [backgroundLight, background],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // Helper Methods
  static Color getPetTypeColor(String petType) {
    switch (petType.toLowerCase()) {
      case 'dog':
        return dogColor;
      case 'cat':
        return catColor;
      case 'bird':
        return birdColor;
      case 'rabbit':
        return rabbitColor;
      case 'fish':
        return fishColor;
      case 'reptile':
        return reptileColor;
      default:
        return otherColor;
    }
  }
  
  static Color getServiceCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'pet-boarding':
        return boardingColor;
      case 'dog-walking':
        return walkingColor;
      case 'pet-daycare':
        return daycareColor;
      case 'drop-in-visits':
        return visitColor;
      case 'grooming':
        return groomingColor;
      case 'vet-visits':
        return vetColor;
      case 'training':
        return trainingColor;
      case 'pet-sitting':
        return sittingColor;
      default:
        return primary;
    }
  }
  
  static Color getBookingStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return pendingColor;
      case 'confirmed':
        return confirmedColor;
      case 'in-progress':
        return inProgressColor;
      case 'completed':
        return completedColor;
      case 'cancelled':
      case 'no-show':
        return cancelledColor;
      case 'refunded':
        return refundedColor;
      default:
        return grey;
    }
  }
  
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }
  
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
  
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}
