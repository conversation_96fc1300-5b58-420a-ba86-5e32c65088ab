const mongoose = require('mongoose');

const petSchema = new mongoose.Schema({
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Pet owner is required']
  },
  name: {
    type: String,
    required: [true, 'Pet name is required'],
    trim: true,
    maxlength: [50, 'Pet name cannot exceed 50 characters']
  },
  type: {
    type: String,
    required: [true, 'Pet type is required'],
    enum: ['dog', 'cat', 'bird', 'rabbit', 'hamster', 'fish', 'reptile', 'other'],
    lowercase: true
  },
  breed: {
    type: String,
    trim: true,
    maxlength: [100, 'Breed cannot exceed 100 characters']
  },
  age: {
    years: {
      type: Number,
      min: [0, 'Age cannot be negative'],
      max: [50, 'Age seems unrealistic']
    },
    months: {
      type: Number,
      min: [0, 'Months cannot be negative'],
      max: [11, 'Months should be 0-11']
    }
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'unknown'],
    lowercase: true
  },
  size: {
    type: String,
    enum: ['small', 'medium', 'large', 'extra-large'],
    lowercase: true
  },
  weight: {
    value: {
      type: Number,
      min: [0, 'Weight cannot be negative']
    },
    unit: {
      type: String,
      enum: ['kg', 'lbs'],
      default: 'kg'
    }
  },
  color: {
    type: String,
    trim: true,
    maxlength: [50, 'Color description cannot exceed 50 characters']
  },
  profileImage: {
    type: String,
    default: null
  },
  images: [{
    type: String
  }],
  // Health and behavior information
  isSpayedNeutered: {
    type: Boolean,
    default: false
  },
  isVaccinated: {
    type: Boolean,
    default: false
  },
  vaccinationRecords: [{
    vaccine: String,
    date: Date,
    nextDue: Date,
    veterinarian: String
  }],
  medicalConditions: [{
    condition: {
      type: String,
      required: true
    },
    description: String,
    medication: String,
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe'],
      default: 'mild'
    },
    diagnosedDate: Date
  }],
  allergies: [{
    allergen: String,
    reaction: String,
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe'],
      default: 'mild'
    }
  }],
  medications: [{
    name: String,
    dosage: String,
    frequency: String,
    instructions: String,
    startDate: Date,
    endDate: Date
  }],
  // Behavior and preferences
  temperament: [{
    type: String,
    enum: [
      'friendly', 'aggressive', 'shy', 'playful', 'calm', 'energetic',
      'anxious', 'protective', 'social', 'independent', 'gentle', 'stubborn'
    ]
  }],
  behaviorNotes: {
    type: String,
    maxlength: [1000, 'Behavior notes cannot exceed 1000 characters']
  },
  goodWith: {
    children: {
      type: Boolean,
      default: true
    },
    dogs: {
      type: Boolean,
      default: true
    },
    cats: {
      type: Boolean,
      default: true
    },
    strangers: {
      type: Boolean,
      default: true
    }
  },
  // Care instructions
  feedingInstructions: {
    food: String,
    amount: String,
    frequency: String,
    specialInstructions: String
  },
  exerciseNeeds: {
    type: String,
    enum: ['low', 'moderate', 'high'],
    default: 'moderate'
  },
  specialInstructions: {
    type: String,
    maxlength: [2000, 'Special instructions cannot exceed 2000 characters']
  },
  // Emergency contact
  emergencyContact: {
    veterinarian: {
      name: String,
      phone: String,
      address: String
    },
    emergencyClinic: {
      name: String,
      phone: String,
      address: String
    }
  },
  // Microchip information
  microchip: {
    id: String,
    registeredWith: String,
    registrationDate: Date
  },
  // Activity and preferences
  favoriteActivities: [String],
  fearsDislikes: [String],
  
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for pet's display age
petSchema.virtual('displayAge').get(function() {
  if (!this.age || (!this.age.years && !this.age.months)) {
    return 'Age unknown';
  }
  
  const years = this.age.years || 0;
  const months = this.age.months || 0;
  
  if (years === 0) {
    return `${months} month${months !== 1 ? 's' : ''} old`;
  } else if (months === 0) {
    return `${years} year${years !== 1 ? 's' : ''} old`;
  } else {
    return `${years} year${years !== 1 ? 's' : ''} and ${months} month${months !== 1 ? 's' : ''} old`;
  }
});

// Virtual for weight display
petSchema.virtual('displayWeight').get(function() {
  if (!this.weight || !this.weight.value) {
    return 'Weight not specified';
  }
  return `${this.weight.value} ${this.weight.unit}`;
});

// Index for searching pets by owner
petSchema.index({ owner: 1 });
petSchema.index({ owner: 1, isActive: 1 });

// Pre-remove middleware to clean up associated data
petSchema.pre('remove', async function(next) {
  try {
    // Remove pet from any bookings
    await mongoose.model('Booking').updateMany(
      { pet: this._id },
      { $unset: { pet: 1 } }
    );
    next();
  } catch (error) {
    next(error);
  }
});

module.exports = mongoose.model('Pet', petSchema);
