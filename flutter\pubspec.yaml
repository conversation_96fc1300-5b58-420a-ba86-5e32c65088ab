name: petric
description: A pet care mobile application connecting pet owners with trusted pet care providers.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  go_router: ^14.2.7
  flutter_screenutil: ^5.9.3
  
  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5
  
  # HTTP & API
  dio: ^5.4.3+1
  retrofit: ^4.1.0
  json_annotation: ^4.9.0
  
  # Local Storage
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Authentication
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.1
  
  # Image & Media
  image_picker: ^1.1.2
  cached_network_image: ^3.3.1
  photo_view: ^0.15.0
  
  # Maps & Location
  google_maps_flutter: ^2.7.0
  geolocator: ^12.0.0
  geocoding: ^3.0.0
  
  # Payments
  flutter_stripe: ^10.1.1
  
  # Push Notifications
  firebase_core: ^2.31.1
  firebase_messaging: ^14.9.4
  flutter_local_notifications: ^17.2.2
  
  # Real-time Communication
  socket_io_client: ^2.0.3+1
  
  # Date & Time
  intl: ^0.20.2
  table_calendar: ^3.1.2
  
  # Utilities
  permission_handler: ^11.3.1
  url_launcher: ^6.3.0
  package_info_plus: ^8.0.0
  device_info_plus: ^10.1.2
  connectivity_plus: ^6.0.5
  
  # UI Components
  flutter_svg: ^2.0.10+1
  shimmer: ^3.0.0
  lottie: ^3.1.2
  flutter_rating_bar: ^4.0.1
  
  # Form & Validation
  flutter_form_builder: ^10.1.0
  form_builder_validators: ^11.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.12
  retrofit_generator: ^8.1.2
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1
  
  # Linting
  flutter_lints: ^4.0.0
  
  # Testing
  mockito: ^5.4.4
  bloc_test: ^9.1.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
    
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
