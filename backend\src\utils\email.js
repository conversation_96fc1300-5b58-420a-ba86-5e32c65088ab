const nodemailer = require('nodemailer');
const path = require('path');
const fs = require('fs').promises;

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: process.env.EMAIL_PORT == 465, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });
};

// Email templates
const emailTemplates = {
  'email-verification': {
    subject: 'Welcome to Petric - Verify Your Email',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center;">
          <h1>Welcome to Petric!</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hi {{firstName}},</h2>
          <p>Thank you for joining Petric, your trusted pet care platform!</p>
          <p>To complete your registration, please verify your email address by clicking the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{verificationLink}}" style="background-color: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">{{verificationLink}}</p>
          <p>This verification link will expire in 24 hours.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px;">
            If you didn't create an account with Petric, please ignore this email.
          </p>
        </div>
        <div style="background-color: #f8f8f8; padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>&copy; 2024 Petric. All rights reserved.</p>
        </div>
      </div>
    `
  },
  
  'password-reset': {
    subject: 'Reset Your Petric Password',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center;">
          <h1>Password Reset</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hi {{firstName}},</h2>
          <p>We received a request to reset your password for your Petric account.</p>
          <p>Click the button below to reset your password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetLink}}" style="background-color: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">{{resetLink}}</p>
          <p>This password reset link will expire in 1 hour.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px;">
            If you didn't request a password reset, please ignore this email. Your password will remain unchanged.
          </p>
        </div>
        <div style="background-color: #f8f8f8; padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>&copy; 2024 Petric. All rights reserved.</p>
        </div>
      </div>
    `
  },
  
  'booking-confirmation': {
    subject: 'Booking Confirmed - {{serviceName}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center;">
          <h1>Booking Confirmed!</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hi {{customerName}},</h2>
          <p>Great news! Your booking has been confirmed.</p>
          
          <div style="background-color: #f8f8f8; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Booking Details</h3>
            <p><strong>Booking ID:</strong> {{bookingId}}</p>
            <p><strong>Service:</strong> {{serviceName}}</p>
            <p><strong>Provider:</strong> {{providerName}}</p>
            <p><strong>Date:</strong> {{bookingDate}}</p>
            <p><strong>Time:</strong> {{bookingTime}}</p>
            <p><strong>Pet(s):</strong> {{petNames}}</p>
            <p><strong>Total:</strong> {{totalAmount}}</p>
          </div>
          
          <p>Your pet care provider will contact you soon with any additional details.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{bookingLink}}" style="background-color: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              View Booking
            </a>
          </div>
        </div>
        <div style="background-color: #f8f8f8; padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>&copy; 2024 Petric. All rights reserved.</p>
        </div>
      </div>
    `
  },
  
  'booking-reminder': {
    subject: 'Reminder: Your Pet Care Service Tomorrow',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center;">
          <h1>Service Reminder</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hi {{customerName}},</h2>
          <p>This is a friendly reminder about your upcoming pet care service.</p>
          
          <div style="background-color: #f8f8f8; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Tomorrow's Service</h3>
            <p><strong>Service:</strong> {{serviceName}}</p>
            <p><strong>Provider:</strong> {{providerName}}</p>
            <p><strong>Time:</strong> {{bookingTime}}</p>
            <p><strong>Pet(s):</strong> {{petNames}}</p>
          </div>
          
          <p>Please ensure your pet(s) are ready and any special instructions have been communicated to your provider.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{bookingLink}}" style="background-color: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              View Booking Details
            </a>
          </div>
        </div>
        <div style="background-color: #f8f8f8; padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>&copy; 2024 Petric. All rights reserved.</p>
        </div>
      </div>
    `
  }
};

// Replace template variables
const replaceTemplateVariables = (template, data) => {
  let result = template;
  for (const [key, value] of Object.entries(data)) {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, value || '');
  }
  return result;
};

// Send email function
const sendEmail = async ({ to, subject, template, data = {}, html, text, attachments = [] }) => {
  try {
    const transporter = createTransporter();
    
    let emailHtml = html;
    let emailSubject = subject;
    
    // Use template if provided
    if (template && emailTemplates[template]) {
      const templateData = emailTemplates[template];
      emailHtml = replaceTemplateVariables(templateData.html, data);
      emailSubject = replaceTemplateVariables(templateData.subject, data);
    }
    
    const mailOptions = {
      from: `"Petric" <${process.env.EMAIL_USER}>`,
      to,
      subject: emailSubject,
      html: emailHtml,
      text,
      attachments
    };
    
    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return result;
    
  } catch (error) {
    console.error('Email sending failed:', error);
    throw error;
  }
};

// Send bulk emails
const sendBulkEmails = async (emails) => {
  const results = [];
  const transporter = createTransporter();
  
  for (const emailData of emails) {
    try {
      const result = await sendEmail(emailData);
      results.push({ success: true, messageId: result.messageId, to: emailData.to });
    } catch (error) {
      results.push({ success: false, error: error.message, to: emailData.to });
    }
  }
  
  return results;
};

// Verify email configuration
const verifyEmailConfig = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('Email configuration is valid');
    return true;
  } catch (error) {
    console.error('Email configuration error:', error);
    return false;
  }
};

module.exports = {
  sendEmail,
  sendBulkEmails,
  verifyEmailConfig,
  emailTemplates
};
