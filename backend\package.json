{"name": "petric-backend", "version": "1.0.0", "description": "Backend API for Petric Pet Care Mobile App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["petcare", "mobile", "api", "nodejs", "express"], "author": "Petric Team", "license": "MIT", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.3", "multer": "^2.0.1", "nodemailer": "^7.0.5", "socket.io": "^4.8.1", "stripe": "^18.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"jest": "^30.0.4", "nodemon": "^3.1.10", "supertest": "^7.1.3"}}