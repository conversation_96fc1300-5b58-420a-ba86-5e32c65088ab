const express = require('express');
const { body, validationResult } = require('express-validator');
const multer = require('multer');
const path = require('path');

const Pet = require('../models/Pet');

const router = express.Router();

// Configure multer for pet image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/pets/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'pet-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG and GIF are allowed.'));
    }
  }
});

/**
 * @swagger
 * /api/pets:
 *   get:
 *     summary: Get user's pets
 *     tags: [Pets]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Pets retrieved successfully
 */
router.get('/', async (req, res) => {
  try {
    const pets = await Pet.find({ 
      owner: req.user.id, 
      isActive: true 
    }).sort({ createdAt: -1 });

    res.json({
      success: true,
      data: { pets }
    });

  } catch (error) {
    console.error('Get pets error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve pets'
    });
  }
});

/**
 * @swagger
 * /api/pets:
 *   post:
 *     summary: Add a new pet
 *     tags: [Pets]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *             properties:
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [dog, cat, bird, rabbit, hamster, fish, reptile, other]
 *               breed:
 *                 type: string
 *               age:
 *                 type: object
 *                 properties:
 *                   years:
 *                     type: number
 *                   months:
 *                     type: number
 *     responses:
 *       201:
 *         description: Pet added successfully
 *       400:
 *         description: Validation error
 */
router.post('/', [
  body('name').trim().isLength({ min: 1, max: 50 }).withMessage('Pet name is required and must be less than 50 characters'),
  body('type').isIn(['dog', 'cat', 'bird', 'rabbit', 'hamster', 'fish', 'reptile', 'other']).withMessage('Invalid pet type'),
  body('breed').optional().trim().isLength({ max: 100 }),
  body('age.years').optional().isInt({ min: 0, max: 50 }),
  body('age.months').optional().isInt({ min: 0, max: 11 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const petData = {
      ...req.body,
      owner: req.user.id
    };

    const pet = new Pet(petData);
    await pet.save();

    res.status(201).json({
      success: true,
      message: 'Pet added successfully',
      data: { pet }
    });

  } catch (error) {
    console.error('Add pet error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add pet'
    });
  }
});

/**
 * @swagger
 * /api/pets/{id}:
 *   get:
 *     summary: Get pet by ID
 *     tags: [Pets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Pet retrieved successfully
 *       404:
 *         description: Pet not found
 */
router.get('/:id', async (req, res) => {
  try {
    const pet = await Pet.findOne({
      _id: req.params.id,
      owner: req.user.id,
      isActive: true
    });

    if (!pet) {
      return res.status(404).json({
        success: false,
        message: 'Pet not found'
      });
    }

    res.json({
      success: true,
      data: { pet }
    });

  } catch (error) {
    console.error('Get pet error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve pet'
    });
  }
});

/**
 * @swagger
 * /api/pets/{id}:
 *   put:
 *     summary: Update pet
 *     tags: [Pets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Pet updated successfully
 *       404:
 *         description: Pet not found
 */
router.put('/:id', [
  body('name').optional().trim().isLength({ min: 1, max: 50 }),
  body('type').optional().isIn(['dog', 'cat', 'bird', 'rabbit', 'hamster', 'fish', 'reptile', 'other']),
  body('breed').optional().trim().isLength({ max: 100 }),
  body('age.years').optional().isInt({ min: 0, max: 50 }),
  body('age.months').optional().isInt({ min: 0, max: 11 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const pet = await Pet.findOneAndUpdate(
      { _id: req.params.id, owner: req.user.id, isActive: true },
      req.body,
      { new: true, runValidators: true }
    );

    if (!pet) {
      return res.status(404).json({
        success: false,
        message: 'Pet not found'
      });
    }

    res.json({
      success: true,
      message: 'Pet updated successfully',
      data: { pet }
    });

  } catch (error) {
    console.error('Update pet error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update pet'
    });
  }
});

/**
 * @swagger
 * /api/pets/{id}:
 *   delete:
 *     summary: Delete pet (soft delete)
 *     tags: [Pets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Pet deleted successfully
 *       404:
 *         description: Pet not found
 */
router.delete('/:id', async (req, res) => {
  try {
    const pet = await Pet.findOneAndUpdate(
      { _id: req.params.id, owner: req.user.id, isActive: true },
      { isActive: false },
      { new: true }
    );

    if (!pet) {
      return res.status(404).json({
        success: false,
        message: 'Pet not found'
      });
    }

    res.json({
      success: true,
      message: 'Pet deleted successfully'
    });

  } catch (error) {
    console.error('Delete pet error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete pet'
    });
  }
});

module.exports = router;
