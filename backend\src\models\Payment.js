const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  // Payment identification
  paymentId: {
    type: String,
    unique: true,
    required: true,
    index: false // Remove duplicate index
  },
  
  // Related entities
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: [true, 'Booking is required']
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  provider: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Provider is required']
  },
  
  // Payment amounts
  amount: {
    subtotal: {
      type: Number,
      required: true,
      min: [0, 'Subtotal cannot be negative']
    },
    tax: {
      type: Number,
      default: 0,
      min: [0, 'Tax cannot be negative']
    },
    serviceFee: {
      type: Number,
      default: 0,
      min: [0, 'Service fee cannot be negative']
    },
    processingFee: {
      type: Number,
      default: 0,
      min: [0, 'Processing fee cannot be negative']
    },
    discount: {
      type: Number,
      default: 0,
      min: [0, 'Discount cannot be negative']
    },
    total: {
      type: Number,
      required: true,
      min: [0, 'Total cannot be negative']
    },
    currency: {
      type: String,
      default: 'USD',
      uppercase: true
    }
  },
  
  // Discount information
  promoCode: {
    code: String,
    discountType: {
      type: String,
      enum: ['percentage', 'fixed'],
      default: 'percentage'
    },
    discountValue: Number,
    appliedAmount: Number
  },
  
  // Payment method and status
  paymentMethod: {
    type: {
      type: String,
      enum: ['card', 'paypal', 'apple-pay', 'google-pay', 'bank-transfer', 'wallet'],
      required: true
    },
    // Card details (last 4 digits, brand)
    card: {
      last4: String,
      brand: String,
      expiryMonth: Number,
      expiryYear: Number
    },
    // Digital wallet info
    wallet: {
      type: String,
      email: String
    }
  },
  
  status: {
    type: String,
    enum: [
      'pending',           // Payment initiated but not completed
      'processing',        // Payment is being processed
      'succeeded',         // Payment completed successfully
      'failed',           // Payment failed
      'cancelled',        // Payment cancelled by user
      'refunded',         // Full refund issued
      'partially-refunded', // Partial refund issued
      'disputed',         // Payment disputed/chargeback
      'expired'           // Payment expired (for pending payments)
    ],
    default: 'pending'
  },
  
  // Stripe integration
  stripe: {
    paymentIntentId: String,
    chargeId: String,
    customerId: String,
    paymentMethodId: String,
    clientSecret: String,
    receiptUrl: String
  },
  
  // Payment timeline
  timeline: {
    initiatedAt: {
      type: Date,
      default: Date.now
    },
    authorizedAt: Date,
    capturedAt: Date,
    failedAt: Date,
    cancelledAt: Date,
    refundedAt: Date
  },
  
  // Refund information
  refunds: [{
    refundId: String,
    amount: {
      type: Number,
      min: [0, 'Refund amount cannot be negative']
    },
    reason: {
      type: String,
      enum: [
        'requested-by-customer',
        'cancelled-by-provider',
        'service-not-provided',
        'dispute-resolution',
        'system-error',
        'other'
      ]
    },
    status: {
      type: String,
      enum: ['pending', 'succeeded', 'failed', 'cancelled'],
      default: 'pending'
    },
    stripeRefundId: String,
    processedAt: Date,
    initiatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Provider payout information
  payout: {
    amount: {
      type: Number,
      min: [0, 'Payout amount cannot be negative']
    },
    platformFee: {
      type: Number,
      default: 0,
      min: [0, 'Platform fee cannot be negative']
    },
    netAmount: {
      type: Number,
      min: [0, 'Net amount cannot be negative']
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'paid', 'failed', 'on-hold'],
      default: 'pending'
    },
    scheduledFor: Date,
    paidAt: Date,
    stripeTransferId: String,
    failureReason: String
  },
  
  // Transaction fees
  fees: {
    stripe: {
      type: Number,
      default: 0,
      min: [0, 'Stripe fee cannot be negative']
    },
    platform: {
      type: Number,
      default: 0,
      min: [0, 'Platform fee cannot be negative']
    },
    total: {
      type: Number,
      default: 0,
      min: [0, 'Total fees cannot be negative']
    }
  },
  
  // Payment metadata
  metadata: {
    ipAddress: String,
    userAgent: String,
    source: {
      type: String,
      enum: ['mobile-app', 'web', 'admin'],
      default: 'mobile-app'
    },
    riskScore: Number,
    fraudCheck: {
      passed: Boolean,
      score: Number,
      details: mongoose.Schema.Types.Mixed
    }
  },
  
  // Error information
  error: {
    code: String,
    message: String,
    details: mongoose.Schema.Types.Mixed,
    occurredAt: Date
  },
  
  // Invoice information
  invoice: {
    number: String,
    url: String,
    generatedAt: Date
  },
  
  // Dispute information
  dispute: {
    id: String,
    reason: String,
    status: String,
    amount: Number,
    evidence: mongoose.Schema.Types.Mixed,
    createdAt: Date,
    updatedAt: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
paymentSchema.index({ paymentId: 1 }, { unique: true });
paymentSchema.index({ booking: 1 });
paymentSchema.index({ customer: 1, status: 1 });
paymentSchema.index({ provider: 1, 'payout.status': 1 });
paymentSchema.index({ status: 1, createdAt: -1 });
paymentSchema.index({ 'stripe.paymentIntentId': 1 });
paymentSchema.index({ 'timeline.initiatedAt': 1 });

// Virtual for total refunded amount
paymentSchema.virtual('totalRefunded').get(function() {
  return this.refunds
    .filter(refund => refund.status === 'succeeded')
    .reduce((total, refund) => total + refund.amount, 0);
});

// Virtual for remaining refundable amount
paymentSchema.virtual('refundableAmount').get(function() {
  return Math.max(0, this.amount.total - this.totalRefunded);
});

// Virtual for payment status display
paymentSchema.virtual('statusDisplay').get(function() {
  return this.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
});

// Pre-save middleware to generate payment ID
paymentSchema.pre('save', async function(next) {
  if (this.isNew && !this.paymentId) {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // Find the last payment of the day
    const lastPayment = await this.constructor
      .findOne({ paymentId: new RegExp(`^PAY${year}${month}${day}`) })
      .sort({ paymentId: -1 });
    
    let sequence = 1;
    if (lastPayment) {
      const lastSequence = parseInt(lastPayment.paymentId.slice(-4));
      sequence = lastSequence + 1;
    }
    
    this.paymentId = `PAY${year}${month}${day}${sequence.toString().padStart(4, '0')}`;
  }
  
  // Calculate net payout amount
  if (this.isModified('amount') || this.isModified('fees')) {
    const platformFeeRate = 0.15; // 15% platform fee
    const grossAmount = this.amount.total - this.amount.tax - this.amount.serviceFee;
    this.payout.platformFee = grossAmount * platformFeeRate;
    this.payout.amount = grossAmount - this.payout.platformFee;
    this.payout.netAmount = this.payout.amount - (this.fees.stripe || 0);
  }
  
  next();
});

// Method to create refund
paymentSchema.methods.createRefund = function(amount, reason, initiatedBy, notes) {
  const refund = {
    amount,
    reason,
    initiatedBy,
    notes,
    status: 'pending'
  };
  
  this.refunds.push(refund);
  return this.save();
};

// Method to update refund status
paymentSchema.methods.updateRefundStatus = function(refundId, status, stripeRefundId) {
  const refund = this.refunds.id(refundId);
  if (refund) {
    refund.status = status;
    if (stripeRefundId) refund.stripeRefundId = stripeRefundId;
    if (status === 'succeeded') refund.processedAt = new Date();
    
    // Update payment status if fully refunded
    if (status === 'succeeded' && this.totalRefunded >= this.amount.total) {
      this.status = 'refunded';
      this.timeline.refundedAt = new Date();
    } else if (status === 'succeeded' && this.totalRefunded > 0) {
      this.status = 'partially-refunded';
    }
  }
  
  return this.save();
};

// Method to update payment status with timeline
paymentSchema.methods.updateStatus = function(newStatus, metadata = {}) {
  const oldStatus = this.status;
  this.status = newStatus;
  
  // Update timeline
  const now = new Date();
  switch (newStatus) {
    case 'processing':
      this.timeline.authorizedAt = now;
      break;
    case 'succeeded':
      this.timeline.capturedAt = now;
      break;
    case 'failed':
      this.timeline.failedAt = now;
      if (metadata.error) {
        this.error = {
          ...metadata.error,
          occurredAt: now
        };
      }
      break;
    case 'cancelled':
      this.timeline.cancelledAt = now;
      break;
    case 'refunded':
      this.timeline.refundedAt = now;
      break;
  }
  
  return this.save();
};

// Static method to get payment statistics
paymentSchema.statics.getStats = function(startDate, endDate, providerId = null) {
  const matchStage = {
    'timeline.initiatedAt': {
      $gte: startDate,
      $lte: endDate
    }
  };
  
  if (providerId) {
    matchStage.provider = mongoose.Types.ObjectId(providerId);
  }
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalPayments: { $sum: 1 },
        totalAmount: { $sum: '$amount.total' },
        successfulPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'succeeded'] }, 1, 0] }
        },
        failedPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        totalRefunded: { $sum: '$totalRefunded' },
        averageAmount: { $avg: '$amount.total' }
      }
    }
  ]);
};

module.exports = mongoose.model('Payment', paymentSchema);
